// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:convert';
import 'dart:typed_data';
import 'package:pointycastle/export.dart';

/// E2E message encryption using AES - Compatible with Node.js crypto
abstract class VMessageEncryption {
  // Static encryption key - matches the backend implementation
  static const String _encryptionKey = "SuperUpE2EEncryptionKey2024!@#\$1234";

  /// Encrypts a message using AES-256-CBC encryption
  static String encryptMessage(String message) {
    try {
      if (message.isEmpty) return message;

      // Create key from first 32 characters (256 bits)
      final keyBytes = utf8.encode(_encryptionKey.substring(0, 32));
      final key = Uint8List.fromList(keyBytes);

      // Create zero IV (16 bytes)
      final iv = Uint8List(16);

      // Convert message to bytes
      final messageBytes = utf8.encode(message);

      // Apply PKCS7 padding
      final paddedMessage = _addPKCS7Padding(messageBytes, 16);

      // Create AES cipher
      final cipher = CBCBlockCipher(AESEngine());
      final params = ParametersWithIV(KeyParameter(key), iv);
      cipher.init(true, params);

      // Encrypt
      final encrypted = Uint8List(paddedMessage.length);
      var offset = 0;
      while (offset < paddedMessage.length) {
        offset += cipher.processBlock(paddedMessage, offset, encrypted, offset);
      }

      // Return base64 encoded result
      return base64.encode(encrypted);
    } catch (e) {
      // If encryption fails, return original message to prevent app crashes
      // Encryption error: $e
      return message;
    }
  }

  /// Decrypts a message using AES-256-CBC decryption
  static String deCryptMessage(String encryptedMessage) {
    try {
      if (encryptedMessage.isEmpty) return encryptedMessage;

      // Create key from first 32 characters (256 bits)
      final keyBytes = utf8.encode(_encryptionKey.substring(0, 32));
      final key = Uint8List.fromList(keyBytes);

      // Create zero IV (16 bytes)
      final iv = Uint8List(16);

      // Decode base64
      final encryptedBytes = base64.decode(encryptedMessage);

      // Create AES cipher
      final cipher = CBCBlockCipher(AESEngine());
      final params = ParametersWithIV(KeyParameter(key), iv);
      cipher.init(false, params);

      // Decrypt
      final decrypted = Uint8List(encryptedBytes.length);
      var offset = 0;
      while (offset < encryptedBytes.length) {
        offset +=
            cipher.processBlock(encryptedBytes, offset, decrypted, offset);
      }

      // Remove PKCS7 padding
      final unpaddedMessage = _removePKCS7Padding(decrypted);

      // Convert back to string
      return utf8.decode(unpaddedMessage);
    } catch (e) {
      // If decryption fails, return original message (might be unencrypted)
      // Decryption error: $e
      return encryptedMessage;
    }
  }

  /// Add PKCS7 padding to data
  static Uint8List _addPKCS7Padding(List<int> data, int blockSize) {
    final paddingLength = blockSize - (data.length % blockSize);
    final paddedData = Uint8List(data.length + paddingLength);
    paddedData.setRange(0, data.length, data);
    for (int i = data.length; i < paddedData.length; i++) {
      paddedData[i] = paddingLength;
    }
    return paddedData;
  }

  /// Remove PKCS7 padding from data
  static Uint8List _removePKCS7Padding(Uint8List data) {
    if (data.isEmpty) return data;

    final paddingLength = data.last;
    if (paddingLength > data.length || paddingLength == 0) {
      return data; // Invalid padding
    }

    // Verify padding
    for (int i = data.length - paddingLength; i < data.length; i++) {
      if (data[i] != paddingLength) {
        return data; // Invalid padding
      }
    }

    return data.sublist(0, data.length - paddingLength);
  }
}
